#!/usr/bin/env python3
"""
Simple API test to verify OMS service is working
"""

import requests
import json
import sys

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        print(f"Health Status: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
            return True
        else:
            print(f"Health check failed: {response.text}")
            return False
    except Exception as e:
        print(f"Health check error: {e}")
        return False

def test_app_orders():
    """Test APP get orders endpoint"""
    try:
        # Using a simple token for testing
        headers = {"Authorization": "test_token"}
        response = requests.get(
            "http://localhost:8000/app/v1/orders?page_size=5&page=1", 
            headers=headers,
            timeout=10
        )
        print(f"APP Orders Status: {response.status_code}")
        if response.status_code in [200, 401]:  # 401 is expected without valid token
            print("APP Orders endpoint is accessible")
            return True
        else:
            print(f"APP Orders failed: {response.text}")
            return False
    except Exception as e:
        print(f"APP Orders error: {e}")
        return False

def test_pos_orders():
    """Test POS get orders endpoint"""
    try:
        # Using a simple token for testing
        headers = {"Authorization": "test_token"}
        response = requests.get(
            "http://localhost:8000/pos/v1/orders?page_size=5&page=1", 
            headers=headers,
            timeout=10
        )
        print(f"POS Orders Status: {response.status_code}")
        if response.status_code in [200, 401]:  # 401 is expected without valid token
            print("POS Orders endpoint is accessible")
            return True
        else:
            print(f"POS Orders failed: {response.text}")
            return False
    except Exception as e:
        print(f"POS Orders error: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Running Simple API Tests...")
    
    # Test health
    if not test_health():
        print("❌ Health check failed. API may not be running.")
        sys.exit(1)
    
    print("\n" + "="*50)
    
    # Test APP endpoints
    print("Testing APP endpoints...")
    test_app_orders()
    
    print("\n" + "="*50)
    
    # Test POS endpoints  
    print("Testing POS endpoints...")
    test_pos_orders()
    
    print("\n✅ Simple API tests completed!")

if __name__ == "__main__":
    main()
