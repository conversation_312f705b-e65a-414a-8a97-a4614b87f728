#!/bin/bash

# OMS API Testing Script
# Tests all APP and POS APIs from the Postman collection

BASE_URL="http://localhost:8000"
APP_BASE="$BASE_URL/app/v1"
POS_BASE="$BASE_URL/pos/v1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "🚀 Starting OMS API Testing..."

# Test Health Endpoint
echo -e "\n${YELLOW}Testing Health Endpoint...${NC}"
health_response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json http://localhost:8000/health)
if [ "$health_response" = "200" ]; then
    echo -e "${GREEN}✅ Health check passed${NC}"
    cat /tmp/health_response.json
else
    echo -e "${RED}❌ Health check failed: $health_response${NC}"
    exit 1
fi

# Get APP Token
echo -e "\n${YELLOW}Getting APP Authentication Token...${NC}"
app_token=$(curl -s -X POST "https://securetoken.googleapis.com/v1/token?key=AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI" \
  -H "Content-Type: application/json" \
  -d '{"grant_type": "refresh_token", "refresh_token": "AMf-vBwuDcyi3Uyhhx1u9QuEJtnnRgcMC3x06S9WRFSVB8R3DLZDP36-ezs01zEa34pg7wdRC6D_1N2-mmdKwx6Hm53te4-yLPBQhwo0cPZPje6IPRj96DEFxcjVP8NhN982COAvQc7YjCNkFIxHgky2rkRxvrGWlX5GDPdAyc43aH6o2OFkgKwyFt08dq1XrJrepQGevu0h27Cg8rZJJsuXtuRGFCHeJeoSn4stYT9BBSfMELG1s5U"}' | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$app_token" ]; then
    echo -e "${YELLOW}⚠️  Using fallback APP token${NC}"
    app_token="fallback_app_token"
else
    echo -e "${GREEN}✅ Got APP token${NC}"
fi

# Get POS Token
echo -e "\n${YELLOW}Getting POS Authentication Token...${NC}"
pos_token=$(curl -s -X POST "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "wZVWpnSIpY", "returnSecureToken": true}' | grep -o '"idToken":"[^"]*"' | cut -d'"' -f4)

if [ -z "$pos_token" ]; then
    echo -e "${YELLOW}⚠️  Using fallback POS token${NC}"
    pos_token="fallback_pos_token"
else
    echo -e "${GREEN}✅ Got POS token${NC}"
fi

# Test APP APIs
echo -e "\n${YELLOW}=== Testing APP APIs ===${NC}"

# 1. Get All Orders (APP)
echo -e "\n1. Testing APP Get All Orders..."
response_code=$(curl -s -w "%{http_code}" -o /tmp/app_orders.json \
  -H "Authorization: $app_token" \
  "$APP_BASE/orders?page_size=20&page=1&sort_order=desc")
echo "Response: $response_code"
if [ "$response_code" = "200" ]; then
    echo -e "${GREEN}✅ APP Get Orders passed${NC}"
else
    echo -e "${RED}❌ APP Get Orders failed${NC}"
    cat /tmp/app_orders.json
fi

# 2. Create Order (APP)
echo -e "\n2. Testing APP Create Order..."
order_data='{
  "customer_id": "test_customer_123",
  "customer_name": "Test Customer",
  "facility_id": "FAC001",
  "facility_name": "Test Facility",
  "total_amount": 100.50,
  "items": [
    {
      "sku": "TEST_SKU_001",
      "name": "Test Product",
      "quantity": 2,
      "unit_price": 25.00,
      "sale_price": 50.25
    }
  ],
  "address": {
    "full_name": "Test Customer",
    "phone_number": "9876543210",
    "address_line1": "123 Test Street",
    "city": "Test City",
    "state": "Test State",
    "postal_code": "123456",
    "country": "India"
  }
}'

response_code=$(curl -s -w "%{http_code}" -o /tmp/app_create_order.json \
  -X POST \
  -H "Authorization: $app_token" \
  -H "Content-Type: application/json" \
  -d "$order_data" \
  "$APP_BASE/create_order")

echo "Response: $response_code"
if [ "$response_code" = "200" ]; then
    echo -e "${GREEN}✅ APP Create Order passed${NC}"
    created_order_id=$(cat /tmp/app_create_order.json | grep -o '"order_id":"[^"]*"' | cut -d'"' -f4)
    echo "Created Order ID: $created_order_id"
else
    echo -e "${RED}❌ APP Create Order failed${NC}"
    cat /tmp/app_create_order.json
fi

# Test POS APIs
echo -e "\n${YELLOW}=== Testing POS APIs ===${NC}"

# 1. Get All Orders (POS)
echo -e "\n1. Testing POS Get All Orders..."
response_code=$(curl -s -w "%{http_code}" -o /tmp/pos_orders.json \
  -H "Authorization: $pos_token" \
  "$POS_BASE/orders?page_size=20&page=1&sort_order=desc")
echo "Response: $response_code"
if [ "$response_code" = "200" ]; then
    echo -e "${GREEN}✅ POS Get Orders passed${NC}"
else
    echo -e "${RED}❌ POS Get Orders failed${NC}"
    cat /tmp/pos_orders.json
fi

# 2. Create Order (POS)
echo -e "\n2. Testing POS Create Order..."
response_code=$(curl -s -w "%{http_code}" -o /tmp/pos_create_order.json \
  -X POST \
  -H "Authorization: $pos_token" \
  -H "Content-Type: application/json" \
  -d "$order_data" \
  "$POS_BASE/create_order")

echo "Response: $response_code"
if [ "$response_code" = "200" ]; then
    echo -e "${GREEN}✅ POS Create Order passed${NC}"
else
    echo -e "${RED}❌ POS Create Order failed${NC}"
    cat /tmp/pos_create_order.json
fi

echo -e "\n${GREEN}🎉 API Testing completed!${NC}"
