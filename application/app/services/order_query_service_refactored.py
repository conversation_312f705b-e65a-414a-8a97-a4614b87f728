from typing import Dict, Optional, List
from fastapi import HTTPException
from app.core.constants import OrderStatus, PaymentStatus, ReturnTypeConstants
from app.utils.order_utils import can_cancel_order
from app.connections.repository import order_repository, payment_repository, invoice_repository
from app.validations.orders import OrderCreateValidator

# Request context
from app.middlewares.request_context import request_context

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger(__name__)

class OrderQueryService:
    """Service for handling order queries (Read operations) using repository pattern"""

    def __init__(self, db_conn=None):
        # Set module name for contextual logging
        request_context.module_name = 'order_query_service'

    def _get_return_type_description(self, return_type_code: str) -> str:
        """Convert return_type code to description using constants"""
        return ReturnTypeConstants.get_description(return_type_code)

    def get_order_by_id(self, order_id: str) -> Optional[Dict]:
        """Get single order by order_id from database with complete details"""
        
        try:
            # Use repository to get order with details
            rows = order_repository.get_order_with_details(order_id)
            if not rows:
                return None

            header = rows[0]
            address = None
            items = []

            for record in rows:
                if address is None and record.get("full_name"):
                    address = {
                        "full_name": record.get("full_name"),
                        "phone_number": record.get("phone_number"),
                        "address_line1": record.get("address_line1"),
                        "address_line2": record.get("address_line2"),
                        "city": record.get("city"),
                        "state": record.get("state"),
                        "postal_code": record.get("postal_code"),
                        "country": record.get("country"),
                        "type_of_address": record.get("type_of_address"),
                        "longitude": float(record.get("longitude")) if record.get("longitude") is not None else None,
                        "latitude": float(record.get("latitude")) if record.get("latitude") is not None else None
                    }

                if record.get("sku"):
                    # Get return type interpretation
                    return_type_code = record.get("return_type", "00")
                    return_type_description = self._get_return_type_description(return_type_code)
                    
                    items.append({
                        "id": record.get("item_id"),
                        "order_id": header.get("id"),
                        "sku": record.get("sku"),
                        "name": record.get("name"),
                        "quantity": record.get("quantity"),
                        "unit_price": float(record.get("unit_price", 0)),
                        "sale_price": float(record.get("sale_price", 0)),
                        "status": OrderStatus.get_customer_status_name(record.get("item_status")),
                        "created_at": record.get("item_created_at"),
                        "updated_at": record.get("item_updated_at"),
                        "cgst": float(record.get("cgst", 0)),
                        "sgst": float(record.get("sgst", 0)),
                        "igst": float(record.get("igst", 0)),
                        "cess": float(record.get("cess", 0)),
                        "is_returnable": record.get("is_returnable", True),
                        "return_type": return_type_description,
                        "return_window": record.get("return_window", 7),
                        "fulfilled_quantity": float(record.get("fulfilled_quantity", 0)),
                        "delivered_quantity": float(record.get("delivered_quantity", 0)),
                        "thumbnail_url": record.get("thumbnail_url")
                    })

            # Get payment details using repository
            payment_rows = payment_repository.get_payment_details_by_external_order_id(order_id)
            payments = []
            for payment_row in payment_rows:
                payments.append({
                    "payment_id": payment_row.get("payment_id"),
                    "payment_amount": float(payment_row.get("payment_amount", 0)),
                    "payment_mode": payment_row.get("payment_mode"),
                    "payment_status": PaymentStatus.get_description(payment_row.get("payment_status"))
                })

            # Get invoice details using repository
            invoice_rows = invoice_repository.get_invoices_by_order_id(order_id)
            invoices = []
            for invoice_row in invoice_rows:
                invoices.append({
                    "raven_link": invoice_row.get("raven_link"),
                    "invoice_s3_url": invoice_row.get("invoice_s3_url")
                })

            return {
                "id": header.get("id"),
                "order_id": header.get("order_id"),
                "customer_id": header.get("customer_id"),
                "customer_name": header.get("customer_name"),
                "facility_id": header.get("facility_id"),
                "facility_name": header.get("facility_name"),
                "status": OrderStatus.get_customer_status_name(header.get("status")),
                "can_cancel": can_cancel_order(header.get("status")),
                "total_amount": float(header.get("total_amount", 0)),
                "eta": header.get("eta"),
                "created_at": header.get("created_at"),
                "updated_at": header.get("updated_at"),
                "address": address,
                "items": items,
                "payments": payments,
                "invoices": invoices
            }

        except Exception as e:
            logger.error(f"order_fetch_error | order_id={order_id} error={e}", exc_info=True)
            raise

    def get_all_orders(self, user_id: str, page_size: int = 20, page: int = 1, sort_order: str = "desc") -> Dict:
        """Get all orders for a user with pagination using repository"""

        try:
            # Validate pagination parameters
            validator = OrderCreateValidator(user_id=user_id)
            validator.validate_page_size(page_size, page)

            # First, get the total count using repository
            total_count = order_repository.count_orders_by_customer(user_id)

            # Validate page bounds
            total_pages = validator.validate_pagination_params(page_size, page, total_count)

            # If no orders, return empty result
            if total_count == 0:
                return {
                    "orders": [],
                    "pagination": {
                        "current_page": page,
                        "page_size": page_size,
                        "total_count": 0,
                        "total_pages": 0,
                        "has_next": False,
                        "has_previous": False
                    }
                }

            # Get orders with items using repository method
            result = order_repository.get_orders_with_items_for_customer(
                user_id, page_size, (page - 1) * page_size, sort_order
            )
            
            orders_data = result.get("orders", [])
            items_by_order = result.get("items_by_order", {})

            orders = []
            for row in orders_data:
                order_id = row.get("id")
                orders.append({
                    "id": order_id,
                    "order_id": row.get("order_id"),
                    "customer_id": row.get("customer_id"),
                    "customer_name": row.get("customer_name"),
                    "facility_id": row.get("facility_id"),
                    "facility_name": row.get("facility_name"),
                    "status": OrderStatus.get_customer_status_name(row.get("status")),
                    "total_amount": float(row.get("total_amount", 0)),
                    "eta": row.get("eta"),
                    "created_at": row.get("created_at"),
                    "updated_at": row.get("updated_at"),
                    "longitude": float(row.get("longitude")) if row.get("longitude") is not None and row.get("longitude") != '' else None,
                    "latitude": float(row.get("latitude")) if row.get("latitude") is not None and row.get("latitude") != '' else None,
                    "history_items": [{
                        "child_sku": item.get("sku"),
                        "thumbnail_url": item.get("thumbnail_url"),
                        "quantity": item.get("quantity", 0),
                        "name": item.get("name", "")
                    } for item in items_by_order.get(order_id, [])]
                })

            return {
                "orders": orders,
                "pagination": {
                    "current_page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_previous": page > 1
                }
            }

        except ValueError as ve:
            logger.error(f"Validation error for user {user_id}: {ve}")
            raise HTTPException(status_code=400, detail=str(ve))
        except Exception as e:
            logger.error(f"orders_fetch_error | user_id={user_id} error={e}", exc_info=True)
            raise HTTPException(status_code=500, detail="Failed to fetch orders")

    def get_order_again_products(self, user_id: str, page_size: int = 20, page: int = 1):
        try:
            # Validate pagination parameters
            validator = OrderCreateValidator(user_id=user_id)
            validator.validate_page_size(page_size, page)

            # First, get the total count using repository
            total_count = order_repository.count_distinct_skus_by_customer(user_id)

            # Validate page bounds
            total_pages = validator.validate_pagination_params(page_size, page, total_count)

            # If no products, return empty result
            if total_count == 0:
                return {
                    "products": [],
                    "pagination": {
                        "current_page": page,
                        "page_size": page_size,
                        "total_count": 0,
                        "total_pages": 0,
                        "has_next": False,
                        "has_previous": False
                    }
                }

            # Get order again products using repository
            products = order_repository.get_order_again_products(user_id, page_size, (page - 1) * page_size)

            return {
                "products": products,
                "pagination": {
                    "current_page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_previous": page > 1
                }
            }

        except ValueError as ve:
            logger.error(f"Validation error for user {user_id}: {ve}")
            raise HTTPException(status_code=400, detail=str(ve))
        except Exception as e:
            logger.error(f"order_again_products_fetch_error | user_id={user_id} error={e}", exc_info=True)
            raise HTTPException(status_code=500, detail="Failed to fetch order again products")

    def get_all_facility_orders(self, facility_name: str, page_size: int = 10, page: int = 1, sort_order: str = "desc") -> Dict:
        """Get all orders for a facility with pagination using repository"""

        try:
            # Validate pagination parameters
            validator = OrderCreateValidator(user_id="facility_user")
            validator.validate_page_size(page_size, page)

            # First, get the total count using repository
            total_count = order_repository.count_orders_by_facility(facility_name)

            # Validate page bounds
            total_pages = validator.validate_pagination_params(page_size, page, total_count)

            # If no orders, return empty result
            if total_count == 0:
                return {
                    "orders": [],
                    "pagination": {
                        "current_page": page,
                        "page_size": page_size,
                        "total_count": 0,
                        "total_pages": 0,
                        "has_next": False,
                        "has_previous": False
                    }
                }

            # Get orders using repository
            rows = order_repository.get_orders_by_facility_with_items(facility_name, page_size, (page - 1) * page_size, sort_order)

            # Group data by order
            orders_dict = {}
            for row in rows:
                order_id = row.get("id")
                if order_id not in orders_dict:
                    orders_dict[order_id] = {
                        "id": order_id,
                        "order_id": row.get("order_id"),
                        "customer_id": row.get("customer_id"),
                        "customer_name": row.get("customer_name"),
                        "facility_id": row.get("facility_id"),
                        "facility_name": row.get("facility_name"),
                        "status": OrderStatus.get_customer_status_name(row.get("status")),
                        "total_amount": float(row.get("total_amount", 0)),
                        "eta": row.get("eta"),
                        "created_at": row.get("created_at"),
                        "updated_at": row.get("updated_at"),
                        "longitude": float(row.get("longitude")) if row.get("longitude") is not None and row.get("longitude") != '' else None,
                        "latitude": float(row.get("latitude")) if row.get("latitude") is not None and row.get("latitude") != '' else None,
                        "items": []
                    }

                # Add items
                if row.get("sku"):
                    orders_dict[order_id]["items"].append({
                        "sku": row.get("sku"),
                        "thumbnail_url": row.get("thumbnail_url"),
                        "quantity": int(row.get("quantity", 0)) if row.get("quantity") is not None else 0,
                        "name": row.get("name") or ""
                    })

            orders = list(orders_dict.values())

            return {
                "orders": orders,
                "pagination": {
                    "current_page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_previous": page > 1
                }
            }

        except ValueError as ve:
            logger.error(f"Validation error for facility {facility_name}: {ve}")
            raise HTTPException(status_code=400, detail=str(ve))
        except Exception as e:
            logger.error(f"facility_orders_fetch_error | facility_name={facility_name} error={e}", exc_info=True)
            raise HTTPException(status_code=500, detail="Failed to fetch facility orders")

    def get_orders_by_customer_id(self, customer_id: str, page_size: int = 20, page: int = 1, sort_order: str = "desc") -> Dict:
        """Get orders by customer ID (same as get_all_orders, maintained for compatibility)"""
        return self.get_all_orders(customer_id, page_size, page, sort_order)

    def get_order_items_by_customer_id(self, customer_id: str) -> List[Dict]:
        """Get all order items for a customer using repository"""
        try:
            rows = order_repository.get_order_items_by_customer(customer_id)
            
            items = []
            for row in rows:
                # Get return type interpretation
                return_type_code = row.get("return_type", "00")
                return_type_description = self._get_return_type_description(return_type_code)
                
                items.append({
                    "id": row.get("id"),
                    "sku": row.get("sku"),
                    "name": row.get("name"),
                    "quantity": row.get("quantity"),
                    "unit_price": float(row.get("unit_price", 0)),
                    "sale_price": float(row.get("sale_price", 0)),
                    "status": OrderStatus.get_customer_status_name(row.get("status")),
                    "created_at": row.get("created_at"),
                    "updated_at": row.get("updated_at"),
                    "cgst": float(row.get("cgst", 0)),
                    "sgst": float(row.get("sgst", 0)),
                    "igst": float(row.get("igst", 0)),
                    "cess": float(row.get("cess", 0)),
                    "is_returnable": row.get("is_returnable", True),
                    "return_type": return_type_description,
                    "return_window": row.get("return_window", 7),
                    "fulfilled_quantity": float(row.get("fulfilled_quantity", 0)),
                    "delivered_quantity": float(row.get("delivered_quantity", 0)),
                    "thumbnail_url": row.get("thumbnail_url")
                })

            return items

        except Exception as e:
            logger.error(f"order_items_fetch_error | customer_id={customer_id} error={e}", exc_info=True)
            raise HTTPException(status_code=500, detail="Failed to fetch order items")

    def get_order_items_by_order_id(self, order_id: str) -> List[Dict]:
        """Get order items for a specific order using repository"""
        try:
            rows = order_repository.get_order_items_by_order(order_id)
            
            items = []
            for row in rows:
                # Get return type interpretation
                return_type_code = row.get("return_type", "00")
                return_type_description = self._get_return_type_description(return_type_code)
                
                items.append({
                    "id": row.get("id"),
                    "sku": row.get("sku"),
                    "name": row.get("name"),
                    "quantity": row.get("quantity"),
                    "unit_price": float(row.get("unit_price", 0)),
                    "sale_price": float(row.get("sale_price", 0)),
                    "status": OrderStatus.get_customer_status_name(row.get("status")),
                    "created_at": row.get("created_at"),
                    "updated_at": row.get("updated_at"),
                    "cgst": float(row.get("cgst", 0)),
                    "sgst": float(row.get("sgst", 0)),
                    "igst": float(row.get("igst", 0)),
                    "cess": float(row.get("cess", 0)),
                    "is_returnable": row.get("is_returnable", True),
                    "return_type": return_type_description,
                    "return_window": row.get("return_window", 7),
                    "fulfilled_quantity": float(row.get("fulfilled_quantity", 0)),
                    "delivered_quantity": float(row.get("delivered_quantity", 0)),
                    "thumbnail_url": row.get("thumbnail_url")
                })

            return items

        except Exception as e:
            logger.error(f"order_items_fetch_error | order_id={order_id} error={e}", exc_info=True)
            raise HTTPException(status_code=500, detail="Failed to fetch order items")
