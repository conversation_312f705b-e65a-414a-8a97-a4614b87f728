from typing import Dict
import random
import string
from datetime import datetime, timedelta, timezone
from app.models.common import get_ist_now
from app.core.constants import OrderStatus, SystemConstants
from app.connections.repository import order_repository

# Request context
from app.middlewares.request_context import request_context

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger(__name__)

def generate_random_prefix() -> str:
    """Generate a random 4-character alphanumeric string for order ID prefix.

    Returns:
        str: A 4-character string containing uppercase letters and digits (e.g., 'A2K4', '489K', 'X7B9')
    """
    characters = string.ascii_uppercase + string.digits
    return ''.join(random.choices(characters, k=4))


class OrderService:
    """Service for handling order commands (Create, Update, Cancel) using SQLAlchemy raw SQL"""

    def __init__(self):
        # Set module name for contextual logging
        request_context.module_name = 'order_service'

    async def get_initial_status(self, origin, payment_modes: list[str]):
        """Decide initial status based on origin and payment modes.
        If any online/razorpay is present → DRAFT, otherwise OPEN.
        """
        if origin == "app" and any(pm in ["razorpay", "online"] for pm in payment_modes):
            return OrderStatus.DRAFT

        return OrderStatus.OPEN

    async def create_order(self, order_data: Dict, origin: str = "app") -> Dict:
        """Create order - Direct write to database with auto-generated order_id using SQLAlchemy"""

        try:
            logger.info(f"order_create_initiated | customer_id={order_data.get('customer_id')} facility_id={order_data.get('facility_id')}")

            # Validation Layer
            required_fields = ['customer_id', 'customer_name', 'facility_id', 'facility_name', 'total_amount']
            for field in required_fields:
                if field not in order_data or not order_data[field]:
                    raise ValueError(f"Missing required field: {field}")

            # Business Logic Layer (Need to be checked )
            # Get current time in IST timezone
            current_time = get_ist_now()
            eta = current_time + timedelta(hours=SystemConstants.DEFAULT_ETA_HOURS)

            logger.info(f"eta_computed | current_time={current_time.isoformat()} eta={eta.isoformat()} eta_hours={SystemConstants.DEFAULT_ETA_HOURS}")

            # Payment Mode
            #payment_mode = order_data.get("payment_mode", "cod")
            #initial_status = await self.get_initial_status(origin, payment_mode)
            if "payment" in order_data and isinstance(order_data["payment"], list):
                payment_modes = [p.get("payment_mode", "cod").lower() for p in order_data["payment"]]
            else:
                payment_modes = [order_data.get("payment_mode", "cod").lower()]
            initial_status = await self.get_initial_status(origin, payment_modes)

            # Generate random prefix for order_id prefix
            random_prefix = generate_random_prefix()

            # Prepare order data for repository
            order_repo_data = {
                'random_prefix': random_prefix,
                'customer_id': order_data['customer_id'],
                'customer_name': order_data['customer_name'],
                'facility_id': order_data['facility_id'],
                'facility_name': order_data['facility_name'],
                'status': initial_status,
                'total_amount': order_data['total_amount'],
                'eta': eta,
                'order_mode': origin,
                'is_approved': order_data.get('is_approved', False)
            }

            # Create order using repository
            order_result = order_repository.create_order_only(order_repo_data)

            if not order_result:
                raise Exception("Failed to create order")

            order_internal_id = order_result['id']
            generated_order_id = order_result['order_id']
            created_at = order_result['created_at']

            logger.info(f"order_row_created | id={order_internal_id} order_id={generated_order_id}")

            # Create order items using repository
            if 'items' in order_data and order_data['items']:
                items_data = []
                for item in order_data['items']:
                    items_data.append({
                        'sku': item['sku'],
                        'name': item.get('name'),
                        'quantity': item['quantity'],
                        'unit_price': item['unit_price'],
                        'sale_price': item['sale_price'],
                        'status': initial_status,
                        'cgst': item.get('cgst', 0.0),
                        'sgst': item.get('sgst', 0.0),
                        'igst': item.get('igst', 0.0),
                        'cess': item.get('cess', 0.0),
                        'is_returnable': item.get('is_returnable', False),
                        'return_type': item.get('return_type', '00'),
                        'return_window': item.get('return_window', 0),
                        'selling_price_net': item.get('selling_price_net', 0.0),
                        'wh_sku': item.get('wh_sku', ''),
                        'pack_uom_quantity': item.get('pack_uom_quantity', 1),
                        'thumbnail_url': item.get('thumbnail_url', None)
                    })

                order_repository.create_order_items_batch(order_internal_id, items_data)

            # Create order address using repository
            if 'address' in order_data and order_data['address']:
                address = order_data['address']
                address_data = {
                    'order_id': order_internal_id,
                    'full_name': address['full_name'],
                    'phone_number': address['phone_number'],
                    'address_line1': address['address_line1'],
                    'address_line2': address.get('address_line2'),
                    'city': address['city'],
                    'state': address['state'],
                    'postal_code': address['postal_code'],
                    'country': address['country'],
                    'type_of_address': address.get('type_of_address', 'delivery'),
                    'longitude': address.get('longitude'),
                    'latitude': address.get('latitude')
                }

                order_repository.create_order_address(address_data)

            logger.info(f"order_create_success | order_id={generated_order_id} id={order_internal_id}")

            # Format ETA in a more readable format
            from app.utils.datetime_helpers import format_datetime_readable
            eta_formatted = format_datetime_readable(eta)

            return {
                "success": True,
                "message": f"Order {generated_order_id} created successfully",
                "order_id": generated_order_id,
                "id": order_internal_id,
                "eta": eta_formatted,
                "created_at": created_at.isoformat() if created_at else None
            }

        except ValueError as validation_error:
            logger.error(f"order_create_validation_error | error={validation_error}")
            return {
                "success": False,
                "message": f"Validation error: {str(validation_error)}"
            }
        except Exception as exc:
            logger.error(f"order_create_unexpected_error | error={exc}", exc_info=True)
            return {
                "success": False,
                "message": f"Unexpected error: {str(exc)}"
            }

    async def update_order_status(self, order_id: str, status) -> Dict:
        """Update order status in both orders and order_items tables using repository"""

        try:
            # Handle both integer constants and string statuses
            if isinstance(status, int):
                # If it's an integer constant, use it directly
                status_value = status
            elif isinstance(status, str):
                # If it's a string, look it up in the mapping
                status_value = OrderStatus.DB_STATUS_MAP.get(status)
                if status_value is None:
                    logger.error(
                        f"order_status_update_invalid_status | status={status}"
                    )
                    return {
                        "success": False,
                        "message": f"Unknown status: {status}"
                    }
            else:
                logger.error(
                    f"order_status_update_invalid_type | status_type={type(status)}"
                )
                return {
                    "success": False,
                    "message": f"Invalid status type: {type(status)}"
                }

            # Update order status using repository
            order_updated = order_repository.update_order_status(order_id, status_value)

            if not order_updated:
                return {
                    "success": False,
                    "message": f"Order {order_id} not found"
                }

            # Update all items status using repository
            items_updated = order_repository.update_order_items_status(order_id, status_value)

            if not items_updated:
                logger.warning(f"No items updated for order {order_id}")

            logger.info(f"order_status_updated | order_id={order_id} status={status}")

            return {
                "success": True,
                "message": f"Order {order_id} status updated to {status}"
            }

        except Exception as e:
            logger.error(f"order_status_update_error | order_id={order_id} error={e}", exc_info=True)
            return {
                "success": False,
                "message": f"Failed to update order status: {str(e)}"
            }

    async def update_item_status(self, order_id: str, sku: str, status: str) -> Dict:
        """Update status of a specific item within an order using repository"""

        try:
            # Get order details using repository
            order_details = order_repository.get_order_by_id(order_id)
            if not order_details:
                return {
                    "success": False,
                    "message": "Order not found"
                }

            # Get item IDs for the SKU
            item_ids = order_repository.get_order_item_ids(order_id, sku)

            if not item_ids:
                return {
                    "success": False,
                    "message": f"Item with SKU '{sku}' not found in order '{order_id}'"
                }

            # Update specific item status using repository
            item_updated = order_repository.update_order_item_status(order_id, item_ids[0], status)

            if not item_updated:
                return {
                    "success": False,
                    "message": f"Failed to update item status"
                }

            logger.info(f"order_item_status_updated | order_id={order_id} sku={sku} status={status}")
            return {
                "success": True,
                "message": f"Item '{sku}' status updated to '{status}'",
                "order_id": order_id,
                "sku": sku,
                "status": status
            }
        except Exception as e:
            logger.error(f"order_item_status_update_error | order_id={order_id} sku={sku} error={e}", exc_info=True)
            return {
                "success": False,
                "message": f"Failed to update item status: {str(e)}"
            }

    async def get_facility_name(self, order_id: str) -> Dict:
        try:
            # Use repository to get order details
            order_details = order_repository.get_order_by_id(order_id)
            if order_details:
                return {"facility_name": order_details['facility_name']}
            return None
        except Exception as e:
            logger.error(f"order_facility_name_error | order_id={order_id} error={e}", exc_info=True)
            return None

    @staticmethod
    def get_return_eligible_items(order_id: str) -> list[Dict]:
        """Return items eligible for full return (status = TMS_DELIVERED) by customer order_id.

        Returns list of dicts: [{"sku": str, "quantity": Decimal}]
        """
        try:
            # Use repository to get delivered items
            delivered_items = order_repository.get_delivered_items_for_return(order_id)
            return [{"sku": item["sku"], "quantity": item["fulfilled_quantity"]} for item in delivered_items]
        except Exception as e:
            logger.error(f"get_return_eligible_items_error | order_id={order_id} error={e}", exc_info=True)
            return []