from __future__ import annotations

from decimal import Decimal
from typing import Dict, List, Optional
import random
import string

from sqlalchemy import text

from app.connections.database import get_raw_transaction
from app.models.common import get_ist_now
from app.logging.utils import get_app_logger
from app.connections.repository import order_repository

logger = get_app_logger(__name__)


def _generate_return_reference() -> str:
    # Example: RTN-25082417-A9ZQ (YYMMDDHH + 4 random)
    now = get_ist_now()
    rand = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
    return f"RTN-{now.strftime('%y%m%d%H')}-{rand}"


class ReturnsService:
    @staticmethod
    def create_return(
        order_id: str,
        items: List[Dict],
        return_reason: Optional[str],
    ) -> Dict:
        """
        Persist a return and its items using a single DB transaction.

        Input items format: [{'sku': str, 'quantity': Decimal|int}]
        Output:
        {
          'return_reference': str,
          'return_id': int,
          'total_refund_amount': Decimal,
          'returned_items': [{'sku': str, 'quantity_returned': int, 'refund_amount': Decimal}]
        }
        """
        returned_items_out: List[Dict] = []
        total_refund: Decimal = Decimal('0.00')

        # Use repository to get order details
        from app.connections.repository import returns_repository

        order_details = returns_repository.validate_order_for_return(order_id)
        if not order_details:
            raise ValueError(f"Order {order_id} not found when creating return")

        order_pk = order_details['id']
        customer_id = order_details['customer_id']
        return_reference = _generate_return_reference()

        # Prepare return data
        return_type = 'full' if len(items) > 0 and all('quantity' in it for it in items) and False else 'partial'

        return_data = {
            'return_reference': return_reference,
            'order_id': order_pk,
            'customer_id': customer_id,
            'return_type': return_type,
            'return_reason': return_reason,
            'total_refund_amount': Decimal('0.00')
        }

        # Create return using repository
        return_result = returns_repository.create_return(return_data)
        if not return_result:
            raise RuntimeError("Failed to create returns row")
        return_id = return_result

        # Process items using repository
        for it in items:
            sku = it['sku']
            qty = it['quantity']
            # Coerce Decimal/int to int safely (validated previously as integer-equivalent)
            qty_int = int(Decimal(qty))

            # Get order item pricing using repository
            order_item = returns_repository.get_order_item_pricing(order_pk, sku)
            if not order_item:
                raise ValueError(f"Order item not found for SKU {sku}")

            unit_price = Decimal(order_item['unit_price'])
            sale_price = Decimal(order_item['sale_price'])
            refund_amount = (sale_price * Decimal(qty_int)).quantize(Decimal('0.01'))

            # Create return item using repository
            return_item_data = {
                'return_id': return_id,
                'order_item_id': order_item['id'],
                'sku': sku,
                'quantity_returned': qty_int,
                'unit_price': unit_price,
                'sale_price': sale_price,
                'refund_amount': refund_amount,
                'return_reason': return_reason,
                'item_condition': None,
                'condition_notes': None,
                'status': 'approved'
            }

            returns_repository.create_return_item(return_item_data)

            returned_items_out.append({
                'sku': sku,
                'quantity_returned': qty_int,
                'refund_amount': float(refund_amount),
            })
            total_refund += refund_amount

        # Update total refund amount using repository
        returns_repository.update_return_total_refund(return_id, total_refund)

        logger.info(
            "return_persisted | order_id=%s return_id=%s return_reference=%s items=%s total_refund=%s",
            order_id, return_id, return_reference, len(items), total_refund
        )

        return {
            'return_reference': return_reference,
            'return_id': return_id,
            'total_refund_amount': float(total_refund),
            'returned_items': returned_items_out
        }