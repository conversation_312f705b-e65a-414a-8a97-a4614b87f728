"""
Order Repository

Repository for order operations using SQLAlchemy with the OMS database.
"""

from typing import Dict, Optional, List
from decimal import Decimal

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger('order_repository')

# Database wrapper
from .main import OMSDatabase


class OrderRepository:
    """Repository for order operations on OMS database"""

    def __init__(self):
        self.db = OMSDatabase()

    def get_order_by_id(self, order_id: str) -> Optional[Dict]:
        """Get order by external order_id"""
        query = """
            SELECT * FROM orders 
            WHERE order_id = %s
        """
        return self.db.fetch_one(query, (order_id,))

    def get_orders_by_customer_id(self, customer_id: str, limit: int = 20, offset: int = 0) -> List[Dict]:
        """Get orders for a customer with pagination"""
        query = """
            SELECT * FROM orders 
            WHERE customer_id = %s
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        """
        return self.db.execute_query(query, (customer_id, limit, offset))

    def update_order_status(self, order_id: str, new_status: int) -> bool:
        """Update order status"""
        query = """
            UPDATE orders 
            SET status = %(new_status)s, updated_at = NOW()
            WHERE order_id = %(order_id)s
        """
        affected_rows = self.db.execute_update(query, {
            'order_id': order_id,
            'new_status': new_status
        })
        return affected_rows > 0

    def update_order_total(self, order_id: str, total_amount: Decimal) -> bool:
        """Update order total amount"""
        query = """
            UPDATE orders 
            SET total_amount = %(total_amount)s, updated_at = NOW()
            WHERE order_id = %(order_id)s
        """
        affected_rows = self.db.execute_update(query, {
            'order_id': order_id,
            'total_amount': total_amount
        })
        return affected_rows > 0

    def get_orders_by_facility(self, facility_id: str, limit: int = 100, offset: int = 0) -> List[Dict]:
        """Get orders for a facility with pagination"""
        query = """
            SELECT * FROM orders 
            WHERE facility_id = %s
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        """
        return self.db.execute_query(query, (facility_id, limit, offset))

    def get_order_with_details(self, order_id: str) -> List[Dict]:
        """Get order with items, address, and basic info for order details page"""
        query = """
            SELECT o.id, o.order_id, o.customer_id, o.customer_name,
                   o.facility_id, o.facility_name,
                   o.status, o.total_amount, o.eta,
                   o.created_at, o.updated_at,
                   oi.id as item_id, oi.sku, oi.name, oi.quantity, oi.unit_price, oi.sale_price, 
                   oi.status AS item_status, oi.cgst, oi.sgst, oi.igst, oi.cess, 
                   oi.is_returnable, oi.return_type, oi.return_window, oi.created_at as item_created_at, 
                   oi.updated_at as item_updated_at, oi.fulfilled_quantity, oi.delivered_quantity,
                   oa.full_name, oa.phone_number, oa.address_line1, oa.address_line2,
                   oa.city, oa.state, oa.postal_code, oa.country, oa.type_of_address,
                   oa.longitude, oa.latitude, oi.thumbnail_url
            FROM orders o
            LEFT JOIN order_items oi ON oi.order_id = o.id
            LEFT JOIN order_addresses oa ON oa.order_id = o.id
            WHERE o.order_id = %s
        """
        return self.db.execute_query(query, (order_id,))

    def get_order_again_products(self, user_id: str, limit: int = 20, offset: int = 0) -> List[str]:
        """Get frequently ordered products for order again functionality"""
        query = """
            SELECT 
                oi.sku,
                COUNT(*) AS order_count
            FROM orders o
            JOIN order_items oi ON oi.order_id = o.id
            WHERE o.customer_id = %s
            GROUP BY oi.sku
            ORDER BY order_count DESC
            LIMIT %s OFFSET %s
        """
        result = self.db.execute_query(query, (user_id, limit, offset))
        return [row.get("sku") for row in result if row.get("sku")]

    def get_orders_with_items_for_customer(self, customer_id: str, limit: int = 20, offset: int = 0, sort_order: str = "desc") -> Dict:
        """Get orders with their items for a customer - optimized version"""
        order_clause = "ORDER BY o.created_at DESC" if sort_order.lower() == "desc" else "ORDER BY o.created_at ASC"
        
        # First get the orders
        orders_query = f"""
            SELECT o.id, o.order_id, o.customer_id, o.customer_name,
                   o.facility_id, o.facility_name,
                   o.status, o.total_amount, o.eta,
                   o.created_at, o.updated_at,
                   oa.longitude, oa.latitude
            FROM orders o
            LEFT JOIN order_addresses oa ON oa.order_id = o.id
            WHERE o.customer_id = %s
            {order_clause}
            LIMIT %s OFFSET %s
        """
        
        orders_data = self.db.execute_query(orders_query, (customer_id, limit, offset))
        
        if not orders_data:
            return {"orders": [], "items_by_order": {}}
        
        # Get all order IDs
        order_ids = [row.get("id") for row in orders_data]
        
        # Get items for all orders in one query
        items_query = """
            SELECT oi.order_id, oi.sku, oi.thumbnail_url, oi.quantity, oi.name
            FROM order_items oi
            WHERE oi.order_id IN ({})
            ORDER BY oi.order_id, oi.id
        """.format(','.join(['%s'] * len(order_ids)))
        
        items_data = self.db.execute_query(items_query, tuple(order_ids))
        
        # Group items by order_id
        items_by_order = {}
        for item_row in items_data:
            order_id = item_row.get("order_id")
            if order_id not in items_by_order:
                items_by_order[order_id] = []
            items_by_order[order_id].append({
                "sku": item_row.get("sku"),
                "thumbnail_url": item_row.get("thumbnail_url"),
                "quantity": int(item_row.get("quantity", 0)) if item_row.get("quantity") is not None else 0,
                "name": item_row.get("name") or ""
            })
        
        return {
            "orders": orders_data,
            "items_by_order": items_by_order
        }

    def create_order(self, order_data: Dict) -> Dict:
        """Create a new order with items and address"""
        operations = []
        
        # Insert order
        order_insert_sql = """
            INSERT INTO orders (
                random_prefix, customer_id, customer_name, 
                facility_id, facility_name, status, total_amount, eta,
                order_mode, is_approved
            ) 
            VALUES (
                %(random_prefix)s, %(customer_id)s, %(customer_name)s, 
                %(facility_id)s, %(facility_name)s, %(status)s, %(total_amount)s, %(eta)s,
                %(order_mode)s, %(is_approved)s
            )
            RETURNING id, order_id, created_at
        """
        
        operations.append({
            'query': order_insert_sql,
            'params': order_data,
            'type': 'insert'
        })
        
        results = self.db.execute_raw_transaction(operations)
        return results[0] if results else None

    def create_order_item(self, item_data: Dict) -> bool:
        """Create a new order item"""
        query = """
            INSERT INTO order_items (
                order_id, sku, name, quantity, unit_price, sale_price, status,
                cgst, sgst, igst, cess, is_returnable, return_type, return_window, 
                selling_price_net, wh_sku, pack_uom_quantity, thumbnail_url
            ) VALUES (
                %(order_id)s, %(sku)s, %(name)s, %(quantity)s, %(unit_price)s, %(sale_price)s, %(status)s,
                %(cgst)s, %(sgst)s, %(igst)s, %(cess)s, %(is_returnable)s, %(return_type)s, %(return_window)s, 
                %(selling_price_net)s, %(wh_sku)s, %(pack_uom_quantity)s, %(thumbnail_url)s
            )
        """
        affected_rows = self.db.execute_update(query, item_data)
        return affected_rows > 0

    def create_order_address(self, address_data: Dict) -> bool:
        """Create a new order address"""
        query = """
            INSERT INTO order_addresses (
                order_id, full_name, phone_number, address_line1, address_line2,
                city, state, postal_code, country, type_of_address, longitude, latitude
            ) VALUES (
                %(order_id)s, %(full_name)s, %(phone_number)s, %(address_line1)s, %(address_line2)s,
                %(city)s, %(state)s, %(postal_code)s, %(country)s, %(type_of_address)s, %(longitude)s, %(latitude)s
            )
        """
        affected_rows = self.db.execute_update(query, address_data)
        return affected_rows > 0

    def update_order_items_status(self, order_id: str, new_status: int) -> bool:
        """Update all order items status for an order"""
        query = """
            UPDATE order_items 
            SET status = %(new_status)s, updated_at = NOW()
            WHERE order_id = (SELECT id FROM orders WHERE order_id = %(order_id)s)
        """
        affected_rows = self.db.execute_update(query, {
            'order_id': order_id,
            'new_status': new_status
        })
        return affected_rows > 0

    def update_order_items_status_by_internal_id(self, internal_order_id: int, new_status: int) -> bool:
        """Update all order items status for an order using internal ID"""
        query = """
            UPDATE order_items 
            SET status = %(new_status)s, updated_at = NOW()
            WHERE order_id = %(order_id)s
        """
        affected_rows = self.db.execute_update(query, {
            'order_id': internal_order_id,
            'new_status': new_status
        })
        return affected_rows > 0

    def update_order_item_status(self, order_id: str, item_id: int, new_status: int) -> bool:
        """Update status of a specific item within an order"""
        query = """
            UPDATE order_items 
            SET status = %(new_status)s, updated_at = NOW()
            WHERE id = %(item_id)s AND order_id = (SELECT id FROM orders WHERE order_id = %(order_id)s)
        """
        affected_rows = self.db.execute_update(query, {
            'order_id': order_id,
            'item_id': item_id,
            'new_status': new_status
        })
        return affected_rows > 0

    def get_order_items_info(self, order_id: str) -> List[Dict]:
        """Get sku, quantity, fulfilled_quantity for order items"""
        query = """
            SELECT sku, quantity, fulfilled_quantity FROM order_items 
            WHERE order_id = (SELECT id FROM orders WHERE order_id = %s)
        """
        return self.db.execute_query(query, (order_id,))

    def get_order_item_ids(self, order_id: str, sku: str) -> List[int]:
        """Get order item IDs for a specific SKU in an order"""
        query = """
            SELECT id FROM order_items
            WHERE order_id = (SELECT id FROM orders WHERE order_id = %s) AND sku = %s
        """
        result = self.db.execute_query(query, (order_id, sku))
        return [row['id'] for row in result]

    def get_order_basic_details(self, order_id: str) -> Optional[Dict]:
        """Get basic order details for validation"""
        query = """
            SELECT id, order_id, status, facility_name 
            FROM orders 
            WHERE order_id = %s
        """
        return self.db.fetch_one(query, (order_id,))

    def count_orders_by_customer(self, customer_id: str) -> int:
        """Count total orders for a customer"""
        query = """
            SELECT COUNT(*) as total_count
            FROM orders o
            WHERE o.customer_id = %s
        """
        result = self.db.fetch_one(query, (customer_id,))
        return result.get('total_count', 0) if result else 0

    def count_distinct_skus_by_customer(self, customer_id: str) -> int:
        """Count distinct SKUs ordered by a customer"""
        query = """
            SELECT COUNT(DISTINCT oi.sku) as total_count
            FROM orders o
            JOIN order_items oi ON oi.order_id = o.id
            WHERE o.customer_id = %s
        """
        result = self.db.fetch_one(query, (customer_id,))
        return result.get('total_count', 0) if result else 0

    def get_orders_by_facility_with_items(self, facility_name: str, limit: int = 10, offset: int = 0, sort_order: str = "desc") -> List[Dict]:
        """Get orders for a facility with items"""
        order_clause = "ORDER BY o.created_at DESC" if sort_order.lower() == "desc" else "ORDER BY o.created_at ASC"
        
        query = f"""
            SELECT o.id, o.order_id, o.customer_id, o.customer_name,
                   o.facility_id, o.facility_name,
                   o.status, o.total_amount, o.eta,
                   o.created_at, o.updated_at,
                   oa.longitude, oa.latitude,
                   oi.sku, oi.thumbnail_url, oi.quantity, oi.name
            FROM orders o
            LEFT JOIN order_addresses oa ON oa.order_id = o.id
            LEFT JOIN order_items oi ON oi.order_id = o.id
            WHERE o.facility_name = %s
            {order_clause}
            LIMIT %s OFFSET %s
        """
        return self.db.execute_query(query, (facility_name, limit, offset))

    def count_orders_by_facility(self, facility_name: str) -> int:
        """Count total orders for a facility"""
        query = """
            SELECT COUNT(*) as total_count
            FROM orders o
            WHERE o.facility_name = %s
        """
        result = self.db.fetch_one(query, (facility_name,))
        return result.get('total_count', 0) if result else 0

    def get_order_items_by_customer(self, customer_id: str) -> List[Dict]:
        """Get all order items for a customer"""
        query = """
            SELECT oi.id, oi.sku, oi.name, oi.quantity, oi.unit_price, oi.sale_price, 
                   oi.status, oi.cgst, oi.sgst, oi.igst, oi.cess, 
                   oi.is_returnable, oi.return_type, oi.return_window, 
                   oi.created_at, oi.updated_at, oi.fulfilled_quantity, oi.delivered_quantity,
                   oi.thumbnail_url
            FROM order_items oi
            JOIN orders o ON o.id = oi.order_id
            WHERE o.customer_id = %s
            ORDER BY oi.created_at DESC
        """
        return self.db.execute_query(query, (customer_id,))

    def get_order_items_by_order(self, order_id: str) -> List[Dict]:
        """Get order items for a specific order"""
        query = """
            SELECT oi.id, oi.sku, oi.name, oi.quantity, oi.unit_price, oi.sale_price, 
                   oi.status, oi.cgst, oi.sgst, oi.igst, oi.cess, 
                   oi.is_returnable, oi.return_type, oi.return_window, 
                   oi.created_at, oi.updated_at, oi.fulfilled_quantity, oi.delivered_quantity,
                   oi.thumbnail_url
            FROM order_items oi
            JOIN orders o ON o.id = oi.order_id
            WHERE o.order_id = %s
            ORDER BY oi.created_at DESC
        """
        return self.db.execute_query(query, (order_id,))


# Global instance for easy import
order_repository = OrderRepository()
