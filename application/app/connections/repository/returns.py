"""
Returns Repository

Repository for returns operations using SQLAlchemy with the OMS database.
"""

from typing import Dict, Any, List, Optional
from decimal import Decimal

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger('returns_repository')

# Database wrapper
from .main import OMSDatabase


class ReturnsRepository:
    """Repository for returns operations on OMS database"""

    def __init__(self):
        """Initialize database connection"""
        self.db = OMSDatabase()

    def create_return(self, return_data: Dict[str, Any]) -> int:
        """Create a new return record"""
        query = """
            INSERT INTO returns (
                return_reference, order_id, customer_id, return_type, return_reason,
                return_method, status, total_refund_amount, refund_status,
                approved_at, processed_at, completed_at, created_at, updated_at
            ) VALUES (
                %(return_reference)s, %(order_id)s, %(customer_id)s, %(return_type)s, %(return_reason)s,
                %(return_method)s, %(status)s, %(total_refund_amount)s, %(refund_status)s,
                %(approved_at)s, %(processed_at)s, %(completed_at)s, NOW(), NOW()
            ) RETURNING id
        """
        return self.db.execute_insert(query, return_data)

    def create_return_item(self, return_item_data: Dict[str, Any]) -> bool:
        """Create a new return item record"""
        query = """
            INSERT INTO return_items (
                return_id, order_item_id, sku, quantity_returned,
                unit_price, sale_price, refund_amount, return_reason,
                item_condition, condition_notes, status, created_at, updated_at
            ) VALUES (
                %(return_id)s, %(order_item_id)s, %(sku)s, %(quantity_returned)s,
                %(unit_price)s, %(sale_price)s, %(refund_amount)s, %(return_reason)s,
                %(item_condition)s, %(condition_notes)s, %(status)s, NOW(), NOW()
            )
        """
        affected_rows = self.db.execute_update(query, return_item_data)
        return affected_rows > 0

    def update_return_total_refund(self, return_id: int, total_refund: Decimal) -> bool:
        """Update total refund amount for a return"""
        query = """
            UPDATE returns
            SET total_refund_amount = %(total_refund)s, updated_at = NOW()
            WHERE id = %(return_id)s
        """
        affected_rows = self.db.execute_update(query, {
            'total_refund': total_refund,
            'return_id': return_id
        })
        return affected_rows > 0

    def get_order_item_pricing(self, order_id: int, sku: str) -> Optional[Dict]:
        """Get order item pricing information"""
        query = """
            SELECT id, unit_price, sale_price
            FROM order_items
            WHERE order_id = %s AND sku = %s
        """
        return self.db.fetch_one(query, (order_id, sku))


# Global instance for easy import
returns_repository = ReturnsRepository()
