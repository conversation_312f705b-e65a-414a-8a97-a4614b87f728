"""
Returns Repository

Repository for returns operations using SQLAlchemy with the OMS database.
"""

from typing import Dict, Any, List, Optional
from decimal import Decimal

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger('returns_repository')

# Database wrapper
from .main import OMSDatabase


class ReturnsRepository:
    """Repository for returns operations on OMS database"""

    def __init__(self):
        """Initialize database connection"""
        self.db = OMSDatabase()

    def create_return(self, return_data: Dict[str, Any]) -> int:
        """Create a new return record"""
        query = """
            INSERT INTO returns (
                return_reference, order_id, customer_id, return_type, return_reason,
                return_method, status, total_refund_amount, refund_status,
                approved_at, processed_at, completed_at, created_at, updated_at
            ) VALUES (
                :return_reference, :order_id, :customer_id, :return_type, :return_reason,
                :return_method, :status, :total_refund_amount, :refund_status,
                :approved_at, :processed_at, :completed_at, NOW(), NOW()
            ) RETURNING id
        """
        return self.db.execute_insert(query, return_data)

    def create_return_item(self, return_item_data: Dict[str, Any]) -> bool:
        """Create a new return item record"""
        query = """
            INSERT INTO return_items (
                return_id, order_item_id, sku, quantity_returned,
                unit_price, sale_price, refund_amount, return_reason,
                item_condition, condition_notes, status, created_at, updated_at
            ) VALUES (
                :return_id, :order_item_id, :sku, :quantity_returned,
                :unit_price, :sale_price, :refund_amount, :return_reason,
                :item_condition, :condition_notes, :status, NOW(), NOW()
            )
        """
        affected_rows = self.db.execute_update(query, return_item_data)
        return affected_rows > 0

    def update_return_total_refund(self, return_id: int, total_refund: Decimal) -> bool:
        """Update total refund amount for a return"""
        query = """
            UPDATE returns
            SET total_refund_amount = :total_refund, updated_at = NOW()
            WHERE id = :return_id
        """
        affected_rows = self.db.execute_update(query, {
            'total_refund': total_refund,
            'return_id': return_id
        })
        return affected_rows > 0

    def get_order_item_pricing(self, order_id: int, sku: str) -> Optional[Dict]:
        """Get order item pricing information"""
        query = """
            SELECT id, unit_price, sale_price
            FROM order_items
            WHERE order_id = %s AND sku = %s
        """
        return self.db.fetch_one(query, (order_id, sku))

    def validate_order_for_return(self, order_id: str) -> Optional[Dict]:
        """Validate order exists and get basic details for return"""
        query = """
            SELECT id, order_id, status, facility_name, customer_id
            FROM orders
            WHERE order_id = %s
        """
        return self.db.fetch_one(query, (order_id,))

    def get_delivered_items_for_return(self, order_internal_id: int) -> List[Dict]:
        """Get delivered items eligible for return"""
        query = """
            SELECT id, sku, quantity, fulfilled_quantity, is_returnable, return_type, return_window
            FROM order_items
            WHERE order_id = %s AND status = %s AND is_returnable = true
        """
        return self.db.execute_query(query, (order_internal_id, 5))  # OrderStatus.TMS_DELIVERED

    def check_existing_return(self, order_internal_id: int, sku: str) -> Optional[Dict]:
        """Check if return already exists for order item"""
        query = """
            SELECT r.id, r.return_reference, ri.quantity_returned
            FROM returns r
            JOIN return_items ri ON r.id = ri.return_id
            JOIN order_items oi ON ri.order_item_id = oi.id
            WHERE oi.order_id = %s AND oi.sku = %s
        """
        return self.db.fetch_one(query, (order_internal_id, sku))

    def create_return_with_transaction(self, return_data: Dict, items: List[Dict]) -> Dict:
        """Create return with items in a single optimized transaction"""
        operations = []

        # Insert return record
        insert_return_sql = """
            INSERT INTO returns (
                return_reference, order_id, customer_id, return_type, return_reason,
                return_method, status, total_refund_amount, refund_status,
                created_at, updated_at
            ) VALUES (
                %(return_reference)s, %(order_id)s, %(customer_id)s, %(return_type)s, %(return_reason)s,
                'api', 'approved', %(total_refund_amount)s, 'pending',
                NOW(), NOW()
            )
            RETURNING id
        """

        operations.append({
            'query': insert_return_sql,
            'params': return_data,
            'type': 'insert'
        })

        # Add return items
        for item in items:
            insert_item_sql = """
                INSERT INTO return_items (
                    return_id, order_item_id, sku, quantity_returned,
                    unit_price, sale_price, refund_amount, return_reason,
                    status, created_at, updated_at
                ) VALUES (
                    %(return_id)s, %(order_item_id)s, %(sku)s, %(quantity_returned)s,
                    %(unit_price)s, %(sale_price)s, %(refund_amount)s, %(return_reason)s,
                    'approved', NOW(), NOW()
                )
            """

            operations.append({
                'query': insert_item_sql,
                'params': item,
                'type': 'insert'
            })

        results = self.db.execute_raw_transaction(operations)
        return {"success": True, "return_id": results[0] if results else None}


# Global instance for easy import
returns_repository = ReturnsRepository()
