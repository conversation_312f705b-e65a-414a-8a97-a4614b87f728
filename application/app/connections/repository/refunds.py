"""
Refund Repository

Repository for refund operations using SQLAlchemy with the OMS database.
"""

from typing import Dict, List, Optional
from decimal import Decimal
from datetime import datetime, timezone

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger('refund_repository')

# Database wrapper
from .main import OMSDatabase


class RefundRepository:
    """Repository for refund operations on OMS database"""

    def __init__(self):
        self.db = OMSDatabase()

    def create_refund_record(self, payment_id: int, refund_id: str, refund_amount: Decimal, refund_status: int = 60, 
                             refund_reason: str = None) -> int:
        """Create a refund record in OMS database"""
        query = """
            INSERT INTO refund_details (
                payment_id, refund_id, refund_amount, refund_status,
                refund_reason, created_at, updated_at
            ) VALUES (
                %(payment_id)s, %(refund_id)s, %(refund_amount)s, %(refund_status)s,
                %(refund_reason)s, NOW(), NOW()
            ) RETURNING id
        """

        params = {
            'payment_id': payment_id,
            'refund_id': refund_id,
            'refund_amount': refund_amount,
            'refund_status': refund_status,
            'refund_reason': refund_reason
        }

        return self.db.execute_insert(query, params)

    def update_refund_status(self, refund_id: str, new_status: int, refund_date: datetime = None) -> bool:
        """Update refund status by refund_id"""
        query = """
            UPDATE refund_details 
            SET refund_status = %(new_status)s, 
                refund_date = %(refund_date)s,
                updated_at = NOW()
            WHERE refund_id = %(refund_id)s
        """

        affected_rows = self.db.execute_update(query, {
            'refund_id': refund_id,
            'new_status': new_status,
            'refund_date': refund_date or datetime.now(timezone.utc)
        })

        return affected_rows > 0

    def get_refund_by_id(self, refund_id: str) -> Optional[Dict]:
        """Get refund record by refund_id"""
        query = """
            SELECT r.*, p.payment_id as external_payment_id, o.order_id as external_order_id
            FROM refund_details r
            JOIN payment_details p ON r.payment_id = p.id
            JOIN orders o ON p.order_id = o.id
            WHERE r.refund_id = %s
        """
        return self.db.fetch_one(query, (refund_id,))

    def get_refunds_for_order(self, order_id: str) -> List[Dict]:
        """Get all refunds for an order"""
        query = """
            SELECT r.*, p.payment_id as external_payment_id, o.order_id as external_order_id
            FROM refund_details r
            JOIN payment_details p ON r.payment_id = p.id
            JOIN orders o ON p.order_id = o.id
            WHERE o.order_id = %s
            ORDER BY r.created_at DESC
        """
        return self.db.execute_query(query, (order_id,))

# Global instance for easy import
refund_repository = RefundRepository()
