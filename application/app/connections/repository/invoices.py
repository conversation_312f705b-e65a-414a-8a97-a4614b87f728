"""
Invoice Repository

Repository for invoice operations using SQLAlchemy with the OMS database.
"""

from typing import Dict, List, Optional

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger('invoice_repository')

# Database wrapper
from .main import OMSDatabase


class InvoiceRepository:
    """Repository for invoice operations on OMS database"""

    def __init__(self):
        self.db = OMSDatabase()

    def get_invoices_by_order_id(self, order_id: str) -> List[Dict]:
        """Get invoice details by order_id"""
        query = """
            SELECT id.raven_link, id.invoice_s3_url
            FROM invoice_details id
            JOIN orders o ON o.id = id.order_id
            WHERE o.order_id = %s
            ORDER BY id.created_at DESC
        """
        return self.db.execute_query(query, (order_id,))

    def create_invoice_record(self, order_id: int, raven_link: str = None, invoice_s3_url: str = None, invoice_data: Dict = None) -> int:
        """Create a new invoice record"""
        query = """
            INSERT INTO invoice_details (
                order_id, raven_link, invoice_s3_url, invoice_data,
                created_at, updated_at
            ) VALUES (
                %(order_id)s, %(raven_link)s, %(invoice_s3_url)s, %(invoice_data)s,
                NOW(), NOW()
            ) RETURNING id
        """

        params = {
            'order_id': order_id,
            'raven_link': raven_link,
            'invoice_s3_url': invoice_s3_url,
            'invoice_data': invoice_data
        }

        return self.db.execute_insert(query, params)

    def update_invoice_urls(self, invoice_id: int, raven_link: str = None, invoice_s3_url: str = None) -> bool:
        """Update invoice URLs"""
        set_clauses = []
        params = {'invoice_id': invoice_id}
        
        if raven_link is not None:
            set_clauses.append("raven_link = %(raven_link)s")
            params['raven_link'] = raven_link
            
        if invoice_s3_url is not None:
            set_clauses.append("invoice_s3_url = %(invoice_s3_url)s")
            params['invoice_s3_url'] = invoice_s3_url
        
        if not set_clauses:
            return False
            
        set_clauses.append("updated_at = NOW()")
        
        query = f"""
            UPDATE invoice_details 
            SET {', '.join(set_clauses)}
            WHERE id = %(invoice_id)s
        """
        
        affected_rows = self.db.execute_update(query, params)
        return affected_rows > 0

    def get_invoice_by_id(self, invoice_id: int) -> Optional[Dict]:
        """Get invoice by internal ID"""
        query = """
            SELECT id.*, o.order_id as external_order_id
            FROM invoice_details id
            JOIN orders o ON o.id = id.order_id
            WHERE id.id = %s
        """
        return self.db.fetch_one(query, (invoice_id,))

# Global instance for easy import
invoice_repository = InvoiceRepository()
