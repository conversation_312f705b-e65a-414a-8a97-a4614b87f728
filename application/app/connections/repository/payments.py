"""
Payment Repository

Repository for payment operations using SQLAlchemy with the OMS database.
"""

from typing import Dict, Any, List, Optional
from decimal import Decimal

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger('payment_repository')

# Database wrapper
from .main import OMSDatabase


class PaymentRepository:
    """Repository for payment operations on OMS database"""

    def __init__(self):
        """Initialize database connection"""
        self.db = OMSDatabase()

    def get_payment_details_by_order_id(self, order_id: int) -> List[Dict]:
        """Get all payment records for an internal order"""
        query = """
            SELECT * FROM payment_details 
            WHERE order_id = %s
            ORDER BY created_at DESC
        """
        return self.db.execute_query(query, (order_id,))

    def get_payment_details_by_external_order_id(self, external_order_id: str) -> List[Dict]:
        """Get all payment records for an external order"""
        query = """
            SELECT pd.payment_id, pd.payment_amount, pd.payment_mode, pd.payment_status
            FROM payment_details pd
            JOIN orders o ON o.id = pd.order_id
            WHERE o.order_id = %s
            ORDER BY pd.created_at DESC
        """
        return self.db.execute_query(query, (external_order_id,))

    def create_payment_record(self, order_id: int, payment_id: str, payment_mode: str, payment_amount: Decimal,
                            payment_status: int = 10,) -> int:
        """Create a new payment record"""
        query = """
            INSERT INTO payment_details (
                order_id, payment_id, payment_mode, payment_amount, 
                payment_status, created_at, updated_at
            ) VALUES (
                %(order_id)s, %(payment_id)s, %(payment_mode)s, %(payment_amount)s,
                %(payment_status)s, NOW(), NOW()
            ) RETURNING id
        """

        params = {
            'order_id': order_id,
            'payment_id': payment_id,
            'payment_mode': payment_mode,
            'payment_amount': payment_amount,
            'payment_status': payment_status
        }

        return self.db.execute_insert(query, params)

    def update_payment_details_status(self, payment_id: str, new_status: int) -> bool:
        """Update payment status"""
        query = """
            UPDATE payment_details 
            SET payment_status = %(new_status)s, updated_at = NOW()
            WHERE payment_id = %(payment_id)s
        """
        affected_rows = self.db.execute_update(query, {'payment_id': payment_id, 'new_status': new_status})
        return affected_rows > 0

payment_repository = PaymentRepository()
