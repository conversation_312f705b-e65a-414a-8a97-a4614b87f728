"""
SQLAlchemy Repository Base Class

This module provides a base database wrapper for repository operations
using SQLAlchemy with proper connection management and error handling.
"""

from typing import Dict, Any, List, Optional
from sqlalchemy import text
from sqlalchemy.orm import Session

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger('repository_main')

# Database connections
from app.connections.database import SessionLocal, ReadSessionLocal


class OMSDatabase:
    """SQLAlchemy wrapper for OMS database operations"""
    
    def __init__(self):
        self.session_class = SessionLocal
        self.read_session_class = ReadSessionLocal

    def get_session(self, read_only: bool = False) -> Session:
        """Get SQLAlchemy session"""
        if read_only:
            return self.read_session_class()
        return self.session_class()

    def execute_query(self, query: str, params: tuple = None, read_only: bool = True) -> List[Dict]:
        """Execute a query and return results as list of dictionaries"""
        session = self.get_session(read_only=read_only)
        try:
            # Convert tuple params to dict if needed for SQLAlchemy text()
            if params:
                if isinstance(params, tuple):
                    # For positional parameters, convert to numbered dict
                    param_dict = {f'param_{i}': val for i, val in enumerate(params)}
                    # Replace %s with :param_0, :param_1, etc.
                    formatted_query = query
                    for i in range(len(params)):
                        formatted_query = formatted_query.replace('%s', f':param_{i}', 1)
                    result = session.execute(text(formatted_query), param_dict)
                else:
                    result = session.execute(text(query), params)
            else:
                result = session.execute(text(query))
            
            columns = result.keys()
            return [dict(zip(columns, row)) for row in result.fetchall()]
            
        except Exception as e:
            logger.error(f"Database query error: {e}")
            raise
        finally:
            session.close()

    def execute_update(self, query: str, params: Dict[str, Any]) -> int:
        """Execute an update query and return affected row count"""
        session = self.get_session(read_only=False)
        try:
            result = session.execute(text(query), params or {})
            session.commit()
            return result.rowcount
        except Exception as e:
            session.rollback()
            logger.error(f"Database update error: {e}")
            raise
        finally:
            session.close()

    def execute_insert(self, query: str, params: Dict[str, Any]) -> int:
        """Execute an insert query and return the inserted ID"""
        session = self.get_session(read_only=False)
        try:
            result = session.execute(text(query), params or {})
            session.commit()
            # For RETURNING queries, fetch the returned value
            row = result.fetchone()
            return row[0] if row else None
        except Exception as e:
            session.rollback()
            logger.error(f"Insert execution error: {e}")
            raise
        finally:
            session.close()

    def fetch_one(self, query: str, params: tuple = None, read_only: bool = True) -> Optional[Dict]:
        """Fetch a single row and return as dictionary"""
        session = self.get_session(read_only=read_only)
        try:
            # Convert tuple params to dict if needed for SQLAlchemy text()
            if params:
                if isinstance(params, tuple):
                    # For positional parameters, convert to numbered dict
                    param_dict = {f'param_{i}': val for i, val in enumerate(params)}
                    # Replace %s with :param_0, :param_1, etc.
                    formatted_query = query
                    for i in range(len(params)):
                        formatted_query = formatted_query.replace('%s', f':param_{i}', 1)
                    result = session.execute(text(formatted_query), param_dict)
                else:
                    result = session.execute(text(query), params)
            else:
                result = session.execute(text(query))
            
            row = result.fetchone()
            if row:
                columns = result.keys()
                return dict(zip(columns, row))
            return None
            
        except Exception as e:
            logger.error(f"Database fetch error: {e}")
            raise
        finally:
            session.close()

    def execute_raw_transaction(self, operations: List[Dict[str, Any]]) -> List[Any]:
        """
        Execute multiple operations in a single transaction
        
        Args:
            operations: List of operations, each containing:
                - query: SQL query string
                - params: Query parameters
                - type: 'select', 'insert', 'update', 'delete'
        
        Returns:
            List of results for each operation
        """
        session = self.get_session(read_only=False)
        results = []
        
        try:
            for operation in operations:
                query = operation.get('query')
                params = operation.get('params', {})
                op_type = operation.get('type', 'select')
                
                result = session.execute(text(query), params)
                
                if op_type == 'select':
                    columns = result.keys()
                    results.append([dict(zip(columns, row)) for row in result.fetchall()])
                elif op_type == 'insert' and 'RETURNING' in query.upper():
                    row = result.fetchone()
                    results.append(row[0] if row else None)
                elif op_type in ['update', 'delete']:
                    results.append(result.rowcount)
                else:
                    results.append(None)
            
            session.commit()
            return results
            
        except Exception as e:
            session.rollback()
            logger.error(f"Transaction error: {e}")
            raise
        finally:
            session.close()
