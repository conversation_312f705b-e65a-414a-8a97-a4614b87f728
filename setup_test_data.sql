-- Setup test data for return flow testing
-- This script creates test orders with delivered status for testing returns

-- Insert test order with delivered status
INSERT INTO orders (
    random_prefix, customer_id, customer_name, facility_id, facility_name,
    status, total_amount, eta, order_mode, is_approved, created_at, updated_at
) VALUES (
    'TEST', 'test_customer_return_001', 'Test Customer Return', 'FAC001', 'Test Facility',
    5, 150.00, NOW() + INTERVAL '2 hours', 'app', true, NOW(), NOW()
) ON CONFLICT DO NOTHING;

-- Get the order ID for the test order
DO $$
DECLARE
    test_order_id INTEGER;
    test_order_public_id VARCHAR;
BEGIN
    -- Get the order details
    SELECT id, order_id INTO test_order_id, test_order_public_id
    FROM orders 
    WHERE customer_id = 'test_customer_return_001' 
    ORDER BY created_at DESC 
    LIMIT 1;
    
    -- Insert test order items with delivered status
    INSERT INTO order_items (
        order_id, sku, name, quantity, unit_price, sale_price, status,
        fulfilled_quantity, delivered_quantity, is_returnable, return_type, return_window,
        created_at, updated_at
    ) VALUES (
        test_order_id, 'RETURN_TEST_SKU_001', 'Test Return Product 1', 2, 25.00, 50.00, 5,
        2, 2, true, '01', 7, NOW(), NOW()
    ), (
        test_order_id, 'RETURN_TEST_SKU_002', 'Test Return Product 2', 1, 50.00, 100.00, 5,
        1, 1, true, '01', 7, NOW(), NOW()
    ) ON CONFLICT DO NOTHING;
    
    -- Insert order address
    INSERT INTO order_addresses (
        order_id, full_name, phone_number, address_line1, address_line2,
        city, state, postal_code, country, type_of_address, longitude, latitude,
        created_at, updated_at
    ) VALUES (
        test_order_id, 'Test Customer Return', '9876543210', '123 Test Return Street', '',
        'Test City', 'Test State', '123456', 'India', 'delivery', NULL, NULL,
        NOW(), NOW()
    ) ON CONFLICT DO NOTHING;
    
    RAISE NOTICE 'Test order created with ID: %', test_order_public_id;
END $$;

-- Create another test order for cancellation testing
INSERT INTO orders (
    random_prefix, customer_id, customer_name, facility_id, facility_name,
    status, total_amount, eta, order_mode, is_approved, created_at, updated_at
) VALUES (
    'CANC', 'test_customer_cancel_001', 'Test Customer Cancel', 'FAC002', 'Test Facility 2',
    1, 75.00, NOW() + INTERVAL '1 hour', 'pos', true, NOW(), NOW()
) ON CONFLICT DO NOTHING;

-- Get the cancellation test order ID and add items
DO $$
DECLARE
    cancel_order_id INTEGER;
    cancel_order_public_id VARCHAR;
BEGIN
    -- Get the order details
    SELECT id, order_id INTO cancel_order_id, cancel_order_public_id
    FROM orders 
    WHERE customer_id = 'test_customer_cancel_001' 
    ORDER BY created_at DESC 
    LIMIT 1;
    
    -- Insert test order items for cancellation
    INSERT INTO order_items (
        order_id, sku, name, quantity, unit_price, sale_price, status,
        fulfilled_quantity, delivered_quantity, is_returnable, return_type, return_window,
        created_at, updated_at
    ) VALUES (
        cancel_order_id, 'CANCEL_TEST_SKU_001', 'Test Cancel Product', 1, 75.00, 75.00, 1,
        0, 0, true, '01', 7, NOW(), NOW()
    ) ON CONFLICT DO NOTHING;
    
    RAISE NOTICE 'Test cancellation order created with ID: %', cancel_order_public_id;
END $$;

-- Display created test orders
SELECT 
    order_id,
    customer_name,
    status,
    total_amount,
    facility_name,
    created_at
FROM orders 
WHERE customer_id IN ('test_customer_return_001', 'test_customer_cancel_001')
ORDER BY created_at DESC;
