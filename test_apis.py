#!/usr/bin/env python3
"""
API Testing Script for OMS Service
Tests all APP and POS APIs from the Postman collection
"""

import requests
import json
import time
from datetime import datetime

# Base URLs
BASE_URL = "http://localhost:8000"
APP_BASE = f"{BASE_URL}/app/v1"
POS_BASE = f"{BASE_URL}/pos/v1"

# Test tokens (these should be obtained from Firebase auth)
APP_TOKEN = "test_app_token"  # Will be replaced with actual token
POS_TOKEN = "test_pos_token"  # Will be replaced with actual token

def get_app_token():
    """Get Firebase APP authentication token"""
    try:
        response = requests.post(
            "https://securetoken.googleapis.com/v1/token?key=AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI",
            headers={"Content-Type": "application/json"},
            json={
                "grant_type": "refresh_token",
                "refresh_token": "AMf-vBwuDcyi3Uyhhx1u9QuEJtnnRgcMC3x06S9WRFSVB8R3DLZDP36-ezs01zEa34pg7wdRC6D_1N2-mmdKwx6Hm53te4-yLPBQhwo0cPZPje6IPRj96DEFxcjVP8NhN982COAvQc7YjCNkFIxHgky2rkRxvrGWlX5GDPdAyc43aH6o2OFkgKwyFt08dq1XrJrepQGevu0h27Cg8rZJJsuXtuRGFCHeJeoSn4stYT9BBSfMELG1s5U"
            }
        )
        if response.status_code == 200:
            return response.json().get("access_token")
    except Exception as e:
        print(f"Failed to get APP token: {e}")
    return None

def get_pos_token():
    """Get Firebase POS authentication token"""
    try:
        response = requests.post(
            "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g",
            headers={"Content-Type": "application/json"},
            json={
                "email": "<EMAIL>",
                "password": "wZVWpnSIpY",
                "returnSecureToken": True
            }
        )
        if response.status_code == 200:
            return response.json().get("idToken")
    except Exception as e:
        print(f"Failed to get POS token: {e}")
    return None

def test_health():
    """Test health endpoint"""
    print("Testing Health Endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Health: {response.status_code} - {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_app_apis(token):
    """Test all APP APIs"""
    print("\n=== Testing APP APIs ===")
    headers = {"Authorization": token}
    
    # Test Get All Orders
    print("1. Testing Get All Orders...")
    try:
        response = requests.get(f"{APP_BASE}/orders?page_size=20&page=1&sort_order=desc", headers=headers)
        print(f"Get Orders: {response.status_code}")
        if response.status_code == 200:
            orders = response.json()
            print(f"Found {len(orders.get('orders', []))} orders")
    except Exception as e:
        print(f"Get Orders failed: {e}")
    
    # Test Create Order
    print("2. Testing Create Order...")
    order_data = {
        "customer_id": "test_customer_123",
        "customer_name": "Test Customer",
        "facility_id": "FAC001",
        "facility_name": "Test Facility",
        "total_amount": 100.50,
        "items": [
            {
                "sku": "TEST_SKU_001",
                "name": "Test Product",
                "quantity": 2,
                "unit_price": 25.00,
                "sale_price": 50.25
            }
        ],
        "address": {
            "full_name": "Test Customer",
            "phone_number": "9876543210",
            "address_line1": "123 Test Street",
            "city": "Test City",
            "state": "Test State",
            "postal_code": "123456",
            "country": "India"
        }
    }
    
    try:
        response = requests.post(f"{APP_BASE}/create_order", headers=headers, json=order_data)
        print(f"Create Order: {response.status_code}")
        if response.status_code == 200:
            order_result = response.json()
            print(f"Created order: {order_result.get('order_id')}")
            return order_result.get('order_id')
    except Exception as e:
        print(f"Create Order failed: {e}")
    
    return None

def test_pos_apis(token):
    """Test all POS APIs"""
    print("\n=== Testing POS APIs ===")
    headers = {"Authorization": token}
    
    # Test Get All Orders
    print("1. Testing POS Get All Orders...")
    try:
        response = requests.get(f"{POS_BASE}/orders?page_size=20&page=1&sort_order=desc", headers=headers)
        print(f"POS Get Orders: {response.status_code}")
    except Exception as e:
        print(f"POS Get Orders failed: {e}")

def setup_test_data_for_returns():
    """Set up test data in database for return flow testing"""
    print("\n=== Setting up test data for returns ===")
    
    # Connect to database and create test order with delivered status
    import psycopg2
    
    try:
        conn = psycopg2.connect(
            host="localhost",
            port="5432",
            database="oms_db",
            user="user",
            password="password"
        )
        cursor = conn.cursor()
        
        # Create test order with delivered status
        cursor.execute("""
            INSERT INTO orders (
                random_prefix, customer_id, customer_name, facility_id, facility_name,
                status, total_amount, eta, order_mode, is_approved
            ) VALUES (
                'TEST', 'test_customer_return', 'Test Customer Return', 'FAC001', 'Test Facility',
                5, 150.00, NOW() + INTERVAL '2 hours', 'app', true
            ) RETURNING id, order_id
        """)
        
        order_result = cursor.fetchone()
        order_internal_id, order_id = order_result
        
        # Create test order items with delivered status
        cursor.execute("""
            INSERT INTO order_items (
                order_id, sku, name, quantity, unit_price, sale_price, status,
                fulfilled_quantity, delivered_quantity, is_returnable, return_type, return_window
            ) VALUES (
                %s, 'RETURN_TEST_SKU_001', 'Test Return Product 1', 2, 25.00, 50.00, 5,
                2, 2, true, '01', 7
            ), (
                %s, 'RETURN_TEST_SKU_002', 'Test Return Product 2', 1, 50.00, 100.00, 5,
                1, 1, true, '01', 7
            )
        """, (order_internal_id, order_internal_id))
        
        conn.commit()
        print(f"Created test order for returns: {order_id}")
        
        cursor.close()
        conn.close()
        
        return order_id
        
    except Exception as e:
        print(f"Failed to setup test data: {e}")
        return None

def test_return_flow(token, order_id):
    """Test return flow with delivered order"""
    print(f"\n=== Testing Return Flow for Order {order_id} ===")
    headers = {"Authorization": token}
    
    # Test Create Return
    return_data = {
        "order_id": order_id,
        "items": [
            {
                "sku": "RETURN_TEST_SKU_001",
                "quantity": 1
            }
        ],
        "return_reason": "Defective product"
    }
    
    try:
        response = requests.post(f"{APP_BASE}/create_return", headers=headers, json=return_data)
        print(f"Create Return: {response.status_code}")
        if response.status_code == 200:
            return_result = response.json()
            print(f"Return created: {return_result}")
        else:
            print(f"Return failed: {response.text}")
    except Exception as e:
        print(f"Create Return failed: {e}")

def main():
    """Main testing function"""
    print("Starting OMS API Testing...")
    
    # Test health first
    if not test_health():
        print("Health check failed. Exiting.")
        return
    
    # Get authentication tokens
    print("\nGetting authentication tokens...")
    app_token = get_app_token()
    pos_token = get_pos_token()
    
    if not app_token:
        print("Using fallback APP token for testing")
        app_token = "test_app_token"
    
    if not pos_token:
        print("Using fallback POS token for testing")
        pos_token = "test_pos_token"
    
    # Test APP APIs
    created_order_id = test_app_apis(app_token)
    
    # Test POS APIs
    test_pos_apis(pos_token)
    
    # Setup test data for returns
    test_order_id = setup_test_data_for_returns()
    
    # Test return flow
    if test_order_id:
        test_return_flow(app_token, test_order_id)
    
    print("\nAPI Testing completed!")

if __name__ == "__main__":
    main()
